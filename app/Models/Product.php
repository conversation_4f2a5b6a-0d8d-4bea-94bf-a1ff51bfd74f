<?php

namespace App\Models;

class Product extends BaseModel
{
    protected $appends = ['available_products'];

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function venueOutlet()
    {
        return $this->belongsTo(VenueOutlets::class, 'outlet_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function taxType()
    {
        return $this->belongsTo(TaxType::class);
    }

    public function productType()
    {
        return $this->belongsTo(ProductType::class);
    }

    public function vouchers()
    {
        return $this->hasMany(Voucher::class, 'product_id', 'id');
    }

    public function availabilities()
    {
        return $this->hasMany(ProductAvailability::class, 'product_id', 'id');
    }

    public function order_items()
    {
        return $this->hasMany(OrderItem::class, 'product_id', 'id');
    }

    public function productCategory()
    {
        return $this->hasOne(ProductCategory::class, 'product_id', 'id');
    }

    public function eventTicket()
    {
        return $this->hasOne(EventTicket::class, 'product_id', 'id');
    }

    public function membershipPackage()
    {
        return $this->hasOne(MembershipPackage::class, 'product_id', 'id');
    }

    public function trainerPackage()
    {
        return $this->hasOne(TrainerPackage::class, 'product_id', 'id');
    }

    public function inventory()
    {
        return $this->hasOne(ProductInventory::class, 'product_id', 'id');
    }

    public function dependencies()
    {
        return $this->hasMany(ProductDependency::class, 'product_id', 'id');
    }

    public function dependedBy()
    {
        return $this->hasMany(ProductDependency::class, 'dependent_id', 'id');
    }
    public function getAvailableProductsAttribute()
    {
        $available_tickets = 0;
        if (isset($this->quantity) && $this->quantity != null) {
            $quantity = $this->quantity;
        } else {
            $quantity = 0;
        }
        if (isset($this->booked_quantity) && $this->booked_quantity != null) {
            $booked_quantity = $this->booked_quantity;
        } else {
            $booked_quantity = 0;
        }

        $available_tickets = $quantity - $booked_quantity;

        return $available_tickets;
    }

    public function rentalProduct(){
        return $this->hasOne(RentalProduct::class,'product_id','id');
    }

    public function productTags(){
        return $this->belongsToMany(ProductTagsConfiguration::class, ProductTag::class, 'product_id', 'tag_id');
    }
    public function variants(){
        return $this->hasMany(Product::class, 'parent_id', 'id');
    }
    public function parent()
    {
        return $this->hasOne(Product::class, 'id', 'parent_id');
    }

    public function SeasonalPricing()
    {
        return $this->hasMany(ProductSeasonalPricing::class,'product_id','id');
    }

    public function memberships(){
        return $this->hasMany(ProductMembership::class,'product_id','id');
    }

    public function SunSystemJvConfiguration()
    {
        return $this->hasOne(SunSystemJvConfiguration::class, 'product_id', 'id');
    }

    public function applicableVenues()
    {
        return $this->belongsToMany(Venue::class, 'product_applicable_venues', 'product_id', 'venue_id');
    }
    public function applicableProducts()
    {
        return $this->hasMany(ProductApplicableVenue::class,'product_id','id');
    }

    /**
     * Generate a unique SKU for the product
     */
    public function generateSku()
    {
        if ($this->sku) {
            return $this->sku;
        }

        $this->sku = self::generateSkuForProduct($this->id, $this->parent_id);
        return $this->sku;
    }

    public static function generateSkuForProduct($id, $parentId = null, $venueId = null)
    {
        $product = self::find($id);
        if (!$product) {
            return null;
        }

        $productType = \App\Models\ProductType::find($product->product_type_id);
        if (!$productType || $productType->name !== 'POS') {
            return null;
        }

        // Get venue code
        $venue = \App\Models\Venue::find($venueId ?: $product->venue_id);
        $venueCode = $venue && $venue->code ? $venue->code : 'DEF';

        if (empty($parentId)) {
            return $venueCode . '-P-' . $id.'-V-0';
        } else {
            // for variants
            $existingVariants = self::where('parent_id', $parentId)
                                   ->where('status_id', 1)
                                   ->where('sku', 'LIKE', $venueCode . '-P-' . $parentId . '-V-%')
                                   ->pluck('sku')
                                   ->toArray();

            $usedNumbers = [];
            foreach ($existingVariants as $sku) {
                if (preg_match('/' . preg_quote($venueCode) . '-P-' . $parentId . '-V-(\d+)$/', $sku, $matches)) {
                    $usedNumbers[] = (int)$matches[1];
                }
            }

            $nextNumber = 1;
            while (in_array($nextNumber, $usedNumbers)) {
                $nextNumber++;
            }

            return $venueCode . '-P-' . $parentId . '-V-' . $nextNumber;
        }
    }

    /**
     *  auto-generate SKU if not provided
     *
     * already there is e validation on front end so it is just an additional step of being safe
     */
    public static function boot()
    {
        parent::boot();

        static::created(function ($product) {
            if (empty($product->sku)) {
                $product->generateSku();
                $product->saveQuietly();
            }
        });
    }
}

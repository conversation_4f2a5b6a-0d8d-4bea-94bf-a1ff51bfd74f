<?php

namespace App\Http\Controllers\pos;

use App\Helper\PosHelper;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Logging;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductType;
use App\Models\TaxType;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as WriterXlsx;


class PosProductController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index(Request $request)
    {
        try {
            $request->request->add(['venue_id' => $this->venueId]);
            $products = PosHelper::getAllProducts($request);
            PosHelper::applyFilters($request, $products);
            $sortBy = 'products.id';
            if ($request->has('order_by')) {
                $sortBy = 'products.' . $request->input('order_by');
            }

            $paginatedResult = $this->paginate($request, $products, $sortBy);

            // Add barcodes to the paginated products
            $responseData = $paginatedResult->getData();
            if ($responseData->status && isset($responseData->data) && count($responseData->data) > 0) {
                $productsCollection = collect($responseData->data);
                \App\Services\BarcodeService::addBarcodesToProducts($productsCollection);

                // Update the response data with the modified products
                $responseData->data = $productsCollection->toArray();
                $paginatedResult->setData($responseData);
            }

            return $paginatedResult;
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function getProduct($id)
    {
        try {
            $req = new Request([
                'id' => $id,
                'venue_id' => $this->venueId
            ]);
            $product = PosHelper::getSingleProduct($req);
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $product], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function storeProduct(Request $request)
    {

        $rules = [
            'name' => 'required|string',
            'price' => 'required|numeric',
            'description' => 'string',
            'image' => 'image|mimes:jpeg,png,jpg|max:4096|dimensions:ratio=1/1',
            'tax_type_id' => 'exists:tax_types,id',
            'category_id' => 'exists:categories,id',
            'sku' => 'nullable|string|max:32|unique:products,sku,NULL,id,venue_id,' . $this->venueId,
            'venue_outlet_id' => 'nullable|exists:venue_outlets,id'
        ];

        $messages = [
            'sku.unique' => 'This SKU is already in use. Please choose a different SKU.',
            'sku.max' => 'SKU cannot be longer than 32 characters.'];

        if ($request->hasFile('image')) {
            $rules['image'] = 'image|mimes:jpeg,png,jpg|max:4096|dimensions:ratio=1/1';
        }
        if ($validate = $this->validationError($request, $rules, $messages)) {
            return $validate;
        }
        try {
            DB::beginTransaction();
            $productTypeID = ProductType::where('name', '=', 'POS')->first();
            if (!$productTypeID) {
                throw new \Exception('POS module not found');
            }
            if (!$request->has('sku') || empty($request->input('sku'))) {
                $request->merge(['generate_sku_after_save' => true]);
            }

            $product =  $this->saveProductCategory($request, $productTypeID->id);
            if ($request->has('isEnableVariant')) {
                $this->saveProductVariants($request, $product);
            }
            DB::commit();
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $product], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' =>  $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function updateProduct(Request $request, $id)
    {
        $rules = [
            'name' => 'required|string',
            'price' => 'required|numeric',
            'description' => 'string',
            'tax_type_id' => 'exists:tax_types,id',
            'category_id' => 'exists:categories,id',
            'sku' => 'nullable|string|max:32|unique:products,sku,' . $id . ',id,venue_id,' . $this->venueId,
            'venue_outlet_id' => 'nullable|exists:venue_outlets,id'
        ];

        $messages = [
            'sku.unique' => 'This SKU is already in use. Please choose a different SKU.',
            'sku.max' => 'SKU cannot be longer than 32 characters.'];

        if ($request->hasFile('image')) {
            $rules['image'] = 'image|mimes:jpeg,png,jpg|max:2048|dimensions:ratio=1/1';
        }
        if ($validate = $this->validationError($request, $rules, $messages)) {
            return $validate;
        }
        if($request->has('deleted_product_variants') && $request->deleted_product_variants){
            $deletedProducts = explode(",",$request->deleted_product_variants);
            if(count($deletedProducts)){
                foreach($deletedProducts as $pid){
                    $res = $this->delete($pid);
                }
            }
        }
        try {
            DB::beginTransaction();
            $request->request->add(['product_id' => $id]);
            $productTypeID = ProductType::where('name', '=', 'POS')->first();
            if (!$productTypeID) {
                throw new \Exception('POS module not found');
            }
            $product =  $this->saveProductCategory($request, $productTypeID->id);
            if($request->has('isEnableVariant')){
                $this->saveProductVariants($request,$product);
            }
            DB::commit();
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $product], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' =>  $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    private function saveProductCategory($request, $productTypeID)
    {
        try {
            $productId = $request->has('product_id') ? $request->input('product_id') : null;
            $image = $request->hasFile('image') ? $request->file('image') : null;
            $product = $this->saveProduct($request, $productTypeID, $productId, $image);

            if (!$productId && !$product->sku && $request->has('generate_sku_after_save')) {
                $parentId = $request->has('parent_id') ? $request->input('parent_id') : null;
                $sku = \App\Models\Product::generateSkuForProduct($product->id, $parentId, $product->venue_id);
                if ($sku) {
                    $product->sku = $sku;
                    $product->save();
                }
            }

            $productCategoryCheck = ProductCategory::where('product_id', $product->id)->where('category_id', $request->input('category_id'))->first();
            if (!$productCategoryCheck) {
                ProductCategory::where('product_id', $product->id)->update(['status_id' => 2]);
                if ($request->has('category_id')) {
                    $productCategory = ProductCategory::updateOrCreate(['product_id' => $product->id, 'category_id' => $request->input('category_id')]);
                    $productCategory->save();
                }
            }
            return $product;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function delete($id)
    {
        try {
            $product = Product::find($id);
            $product->status_id = 2;
            $product->save();
            $message = "Deleted Successfully";

            Product::where('parent_id',$product->id)->update(['status_id' => 2]);
            Logging::create([
                'venue_id' => $this->venueId,
                'user_id' => $this->userId,
                'target_id' => $product->id,
                'model' => get_class($product),
                'table' => $product->getTable(),
                'action' => 'Delete',
                'description' =>  "{$this->user->first_name} {$this->user->last_name} deleted '{$product->name}' product.",
            ]);

            return response()->json(['status' => true, 'message' => $message, 'data' => null], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Get the maximum product ID and venue information for SKU generation
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMaxId()
    {
        try {
            $maxId = Product::max('id');
            $venue = \App\Models\Venue::find($this->venueId);
            $venueCode = $venue && $venue->code ? $venue->code : 'DEF';

            return response()->json([
                'status' => true,
                'message' => 'Maximum product ID retrieved successfully',
                'data' => [
                    'max_id' => $maxId ? $maxId + 1 : 0,
                    'venue_code' => $venueCode,
                    'sku_format' => [
                        'parent' => $venueCode . '-P-{ID}',
                        'variant' => $venueCode . '-P-{PARENT_ID}-V-{VARIANT_NUMBER}'
                    ],
                    'sku_examples' => [
                        'parent' => $venueCode . '-P-' . ($maxId ? $maxId + 1 : 1),
                        'variant' => $venueCode . '-P-' . ($maxId ? $maxId + 1 : 1) . '-V-1'
                    ]
                ]
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $this->getError($e),
                'data' => null
            ], Response::HTTP_CONFLICT);
        }
    }

    /** Pos Product XL Import */
    public function importProducts(Request $request)
    {
        $rules = [
            'type' => 'required',
            'excel_file' => 'required|mimes:xlsx,xls',
        ];
        // TODO: dynamic file validation
        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }
        $productTypeID = ProductType::where('name', '=', $request->type)->first();
        if (!$productTypeID) {
            throw new \Exception('product type module not found');
        }
        $reader = new Xlsx();
        $spreadsheet = $reader->load($request->file('excel_file'));
        if ($spreadsheet) {
            $headers =  [
                "VenueServiceId",
                "Name",
                "ProductDescription",
                "Category",
                "Price",
                "SiteName",
                "GlCode",
                "ProjectNumber",
                "TaskName",
            ];
            $cells = collect($cells = $spreadsheet->getActiveSheet()->toArray());
            // info(json_encode($cells));
            // return $cells;
            /** validate header column name valid or not */
            if (
                isset($cells[0]) && count($cells[0]) == 9 && $cells[0][0] == "VenueServiceId" && $cells[0][1] == "Name" && $cells[0][2] == "ProductDescription" && $cells[0][3] == "Category" && $cells[0][4] == "Price" && $cells[0][5] == "SiteName" && $cells[0][6] == "GlCode" && $cells[0][7] == "ProjectNumber" && $cells[0][8] == "TaskName"
            ) {
                $cells = $cells->map(function ($value, $key) use ($headers) {
                    return collect($value)->mapWithKeys(function ($value, $key) use ($headers) {
                        return [$headers[$key] => $value];
                    });
                })->toArray();
                $invalidData = [];
                $newPayload = [];
                $rowCount = 0;
                $productData = [];
                $i = 0;

                for ($key = 0; $key < count($cells); $key++) {
                    if ($key == 0 || $key === 1) continue;
                    try {
                        $productData[$i]['name'] = $cells[$key]['Name'];
                        $productData[$i]['description'] = $cells[$key]['ProductDescription'];
                        $productData[$i]['category_name'] = $cells[$key]['Category'];
                        $productData[$i]['price'] = $cells[$key]['Price'];
                        $productData[$i]['project_no'] = $cells[$key]['ProjectNumber'];
                        $productData[$i]['gl_code'] = $cells[$key]['GlCode'];
                        $productData[$i]['task_name'] = $cells[$key]['TaskName'];
                        $productData[$i]['tax_type_id'] = 1;
                        $productData[$i]['venue_service_id'] = $cells[$key]['VenueServiceId'];
                        $productData[$i]['transaction_type'] = $cells[$key]['SiteName'];
                        $i++;
                    } catch (Exception $e) {
                        info($e->getMessage());
                        array_push($invalidData, $cells[$key]);
                    }
                }

                /** save products Data */
                DB::beginTransaction();
                if (count($productData)) {
                    $savedDataCount = 0;
                    foreach ($productData as $k => $p) {
                        $p = (object) $p;
                        $category = Category::where(['venue_service_id' => $p->venue_service_id, 'name' => $p->category_name])->first();
                        if (!$category) {
                            $category = new Category();
                            $category->venue_service_id = $p->venue_service_id;
                            $category->status_id = 1;
                            $category->name = $p->category_name;
                            $category->save();
                        }
                        $product = new Product;
                        $product->venue_id = $this->venueId;
                        $product->tax_type_id = $p->price > 0 ? 1 : 0;
                        $product->product_type_id = $productTypeID->id;
                        if (isset($p->description)) {
                            $product->description = $p->description;
                        } else {
                            $product->description = null;
                        }
                        $product->name = $p->name;
                        $product->total_price = $p->price; //post tax price
                        if ($p->price > 0) {
                            $tax_amount = $p->price * (5 / 100);
                            $product->price = $p->price - $tax_amount;
                            $product->tax_amount = $tax_amount;
                        } else {
                            $product->price = $p->price;
                            $tax_amount = 0;
                        }
                        $product->venue_id = $this->venueId;
                        $product->project_no = $p->project_no;
                        $product->task_name = $p->task_name;
                        $product->gl_code = $p->gl_code;

                        $product->save();

                        $productCategory = ProductCategory::updateOrCreate(['product_id' => $product->id, 'category_id' => $category->id]);
                        $productCategory->save();
                        $savedDataCount++;
                    }
                }
                DB::commit();
                info(json_encode($productData));
                return response()->json(['status' => true, 'message' =>  'Pos Product data', 'data' => $savedDataCount], Response::HTTP_OK);
                // return $invalidData;
            } else {
                Db::rollBack();
                return response()->json(['status' => false, 'message' => 'Excel File Mis Matched Column', 'data' => null], Response::HTTP_CONFLICT);
            }
        }
        return "file failed";
    }
    public function exportSuccessFailed($customers, $fileName = "")
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', 'Name');
        $sheet->setCellValue('B1', 'ProductDescription');
        $sheet->setCellValue('C1', 'Category');
        $sheet->setCellValue('D1', 'Price');
        $sheet->setCellValue('E1', 'SiteName');
        $sheet->setCellValue('F1', 'GlCode');
        $sheet->setCellValue('G1', 'ProjectNumber');
        $sheet->setCellValue('H1', 'TaskName');

        $rows = 2;
        foreach ($customers as $data) {
            $sheet->setCellValue('A' . $rows, $data['Name']);
            $sheet->setCellValue('B' . $rows, $data['ProductDescription']);
            $sheet->setCellValue('C' . $rows, $data['Category']);
            $sheet->setCellValue('D' . $rows, $data['Price']);
            $sheet->setCellValue('E' . $rows, $data['SiteName']);
            $sheet->setCellValue('F' . $rows, $data['GlCode']);
            $sheet->setCellValue('G' . $rows, $data['ProjectNumber']);
            $sheet->setCellValue('H' . $rows, $data['TaskName']);
            $rows++;
        }
        if ($fileName == "success") {
            $fileName = "success-product-import-data-" . date("Y-m-d-H-i-s") . ".xlsx";
        } else if ($fileName == "failed") {
            $fileName = "failed-product-import-data-" . date("Y-m-d-H-i-s") . ".xlsx";
        } else {
            $fileName = "product-data-" . date("Y-m-d-H-i-s") . ".xlsx";
        }
        $writer = new WriterXlsx($spreadsheet);
        $writer->save($fileName);
        return public_path() . '/' . $fileName;
    }

    public function saveProductVariants($request,$parentProduct){
        if ($parentProduct) {
            if ($request->has('isEnableVariant') && $request->isEnableVariant && $request->has('product_variants') && count($request->product_variants)) {
                /** validate product variant already exist */
                foreach ($request->product_variants as $pv) {
                    $pv = (object)$pv;
                    $name = $parentProduct->name . ' (' . $pv->attribute_name . ')';
                    if(isset($pv->id) && $pv->id){
                        $checkProduct = Product::where(['parent_id' => $parentProduct->id, 'name' => trim($name),'attribute_name' => trim($pv->attribute_name), 'status_id' => 1, 'venue_id' => $this->venueId])->where('id','!=',$pv->id)->first();
                    }else{
                        $checkProduct = Product::where(['parent_id' => $parentProduct->id, 'name' => trim($name), 'attribute_name' => trim($pv->attribute_name), 'status_id' => 1, 'venue_id' => $this->venueId])->first();
                    }

                    if($checkProduct){
                        throw new Exception($pv->name." product already exist");
                    }

                    // Validate SKU uniqueness for variants
                    if (isset($pv->sku) && !empty($pv->sku)) {
                        $skuCheck = Product::where('sku', $pv->sku)
                                          ->where('venue_id', $this->venueId);
                        if (isset($pv->id) && $pv->id) {
                            $skuCheck->where('id', '!=', $pv->id);
                        }
                        if ($skuCheck->exists()) {
                            throw new Exception("The SKU '{$pv->sku}' is already in use.");
                        }
                    }
                }
                foreach ($request->product_variants as $pv) {
                    $req = [];
                    $pv = (object)$pv;
                    $req['name'] = $parentProduct->name.' ('.$pv->attribute_name.')';
                    $req['attribute_name'] = $pv->attribute_name;
                    $req['price'] = (float)$pv->price;
                    $req['total_price'] = $pv->total_price;
                    $req['pre_tax_price'] = $pv->pre_tax_price;
                    $req['category_id'] = $pv->category_id;
                    $req['tax_type_id'] = $pv->tax_type_id;
                    $req['parent_id'] = $parentProduct->id;

                    // Handle SKU for product variants
                    if (isset($pv->sku) && !empty($pv->sku)) {
                        $req['sku'] = $pv->sku;
                    } else {
                        $variantId = isset($pv->id) ? $pv->id : null;
                        if (!$variantId) {
                            $req['generate_sku_after_save'] = true;
                        }
                    }
                    $req['outlet_id'] = $parentProduct->outlet_id??NULL;
                    $req['venue_outlet_id'] = $parentProduct->venue_outlet_id??NULL;
                    $req['enable_retail_inventory'] = $parentProduct->enable_retail_inventory?$parentProduct->enable_retail_inventory:0;
                    if (isset($pv->id)) {
                        $req['product_id'] = $pv->id;
                    }
                    if (isset($parentProduct->project_no) && $parentProduct->project_no) {
                        $req['project_no'] = $parentProduct->project_no;
                        $req['task_name'] = $parentProduct->task_name ? $parentProduct->task_name : null;
                        $req['gl_code'] = $parentProduct->gl_code ? $parentProduct->gl_code : null;
                        $req['transaction_type'] = $parentProduct->transaction_type ? $parentProduct->transaction_type : null;
                    }
                    $newRequest = new Request;
                    $newRequest->merge($req);
                    $this->saveProductCategory($newRequest, $parentProduct->product_type_id);
                }
            }
        }
    }
}

<?php

namespace App\Helper;

use App\Models\Category;
use App\Models\Logging;
use App\Models\Product;
use App\Models\ProductType;
use App\Models\RetailInventory;
use Illuminate\Support\Facades\DB;

class PosHelper
{

    public static function getAllProducts($request)
    {
        $pos = ProductType::where('name', '=', 'POS')->first();
        if (!$pos) {
            throw new \Exception('POS module not found');
        }
        $venue_id = $request->venue_id;
        $products = Product::with(['productTags' => function ($q)  {
            $q->where('product_tags.status_id', 1);
        }, 'variants' => function($q) use ($venue_id,$pos) {
            $q->select('id','parent_id','name','sku','price','tax_amount','total_price','attribute_name','venue_id','products.enable_retail_inventory','stock_level');
            $q->where(['venue_id' => $venue_id,'status_id' => 1,'product_type_id' => $pos->id]);
            // $q->where(function($qq){
            //     $qq->where('enable_retail_inventory', 0)->orWhere('stock_level', '>', 0);
            // });

        }])
            ->leftJoin('product_categories as pc', 'pc.product_id', '=', 'products.id')
            ->leftJoin('categories as c', 'c.id', '=', 'pc.category_id')
            ->select(
                'products.id as id',
                'products.name',
                'products.sku',
                'products.description',
                'products.image',
                'products.price',
                'products.tax_amount',
                'products.total_price',
                'products.venue_id',
                'products.outlet_id',
                'products.venue_outlet_id',
                'products.is_membership_only',
                'products.benefit_excluded',
                'products.stock_level',
                'products.enable_online_booking',
                'products.enable_retail_inventory',
                'c.name as category'
            )
            ->where(['products.status_id' => 1, 'products.venue_id' => $venue_id, 'pc.status_id' => 1, 'products.product_type_id' => $pos->id])
            ->where('products.parent_id',NULL);
        return $products;
    }

    public static function getSingleProduct($request)
    {
        $id = $request->id;
        $pos = ProductType::where('name', '=', 'POS')->first();
        if (!$pos) {
            throw new \Exception('POS module not found');
        }
        $venue_id = $request->venue_id;

        $products = Product::with(['productTags' => function ($q) {
            $q->where('product_tags.status_id', 1);
        }, 'variants' => function ($q) use ($venue_id, $pos) {
            $q->select('id', 'parent_id', 'name', 'sku', 'price', 'tax_amount', 'total_price', 'venue_id', 'attribute_name');
            $q->where(['venue_id' => $venue_id, 'status_id' => 1, 'product_type_id' => $pos->id]);
        }])
            ->leftJoin('product_categories as pc', 'pc.product_id', '=', 'products.id')
            ->leftJoin('categories as c', 'c.id', '=', 'pc.category_id')
            ->select(
                'products.id as id',
                'products.name',
                'products.sku',
                'products.description',
                'products.image',
                'products.price',
                'products.tax_amount',
                'products.total_price',
                'products.venue_id',
                'products.outlet_id',
                'products.venue_outlet_id',
                'products.is_membership_only',
                'products.benefit_excluded',
                'c.name as category',
                'c.id as category_id',
                'products.tax_type_id as tax_type_id',
                'products.project_no',
                'products.task_name',
                'products.gl_code',
                'products.transaction_type',
                'products.enable_online_booking',
                'products.enable_retail_inventory'
            )
            ->where(['products.status_id' => 1, 'products.venue_id' => $venue_id, 'pc.status_id' => 1, 'products.product_type_id' => $pos->id, 'products.id' => $id]);
        return $products->first();
    }

    public static function applyFilters($request, &$products)
    {
        if ($request->has('category')) {
            if ($request->input('category') != 'all') {
                $categoryArray = explode(',', $request->input('category'));
                $products = $products->whereIn('c.id', $categoryArray);
            }
        }
        if ($request->has('product_name')) {
            $name = $request->input('product_name');
            $products = $products->whereRaw("products.name like '%$name%'");
            //            $products = $products->where('products.name', $request->input('product_name'));
        }
        if ($request->has('outlet_id')) {
            $products = $products->where('products.outlet_id',$request->outlet_id);
        }else{
            $products = $products->whereNull('products.outlet_id');
        }
        if ($request->has('enable_retail_inventory')) {
            $products = $products->where('products.enable_retail_inventory',$request->enable_retail_inventory);
        }
        if($request->has('check_inventory')){
            $products = $products->where(function ($query) {
                $query->whereHas('variants', function ($variantQuery) {
                    // If there are variants, skip the stock_level check
                    // You can customize this condition based on your variant structure
                    $variantQuery->where('enable_retail_inventory', 0)->orWhere('stock_level', '>', 0);
                })->orWhere(function ($subquery) {
                    // If the product does not have variants, apply the stock_level check
                    $subquery->where(function($q){
                        $q->where('products.enable_retail_inventory', 0)->orWhere('products.stock_level', '>', 0);
                    })->whereNull('products.parent_id');
                });
            });
            // $products = $products->where(function($query){
            //     $query->where('products.enable_retail_inventory',0)->orWhere('products.stock_level','>',0);
            // });
        }
        if ($request->has('parent_id') && $request->parent_id  && $request->parent_id != 'null' && $request->parent_id > 0) {
            $parentId = $request->parent_id;
            $products = $products->where('id',$parentId)->with(['variants' => function($query) use($parentId){
                $query->where('parent_id',$parentId);
                $query->where('status_id',1);
            }]);
        }
    }

    public static function getCategories($request)
    {
        $venueService = $request->input('venue_service_id');
        $outletId = $request->has('outlet_id') && $request->outlet_id > 0?$request->outlet_id:null;
        return Category::whereHas('products', function ($q) {
            $q->where('categories.status_id', 1)->where('product_categories.status_id', 1);
        })->where('status_id', 1)->where('venue_service_id', $venueService)->where('outlet_id',$outletId)->get();
    }

    public static function inventoryUpdate($product_id,$stock_qty,$transaction_type = "I",$order_item_id = NULL){
        $insertedId = RetailInventory::insertGetId([
            'product_id' => $product_id,
            'stock_qty' => $stock_qty,
            'transaction_type' => $transaction_type,
            'order_item_id' => $order_item_id,
        ]);
        return $insertedId;
    }
    public static function updateProductStock($product, $stock_qty, $type){
        if($type == "S"){
            $product->stock_level -= $stock_qty;
        }else if($type == "P"){
            $product->stock_level += $stock_qty;
        }else if($type == "R"){
            $product->stock_level += $stock_qty;
        }
        $product->save();
    }
    public static function updateProductAndRetailStockById($productId, $stock_qty, $type,$order_item_id = null){
        $product = Product::where(['id' => $productId,'enable_retail_inventory'=>1])->first();
        if($product && $product->enable_retail_inventory){
            if($type == "S"){
                $product->stock_level -= $stock_qty;
            }else if($type == "I"){
                $product->stock_level += $stock_qty;
            }else if($type == "R"){
                $product->stock_level += $stock_qty;
            }else if($type == "O"){
                /* validation */
                if( $product->stock_level - $stock_qty < 0){
                    throw new \Exception('Product: '.$product->name.' maximum stock out quantity: '.$product->stock_level);
                }
                $product->stock_level -= $stock_qty;
            }
            $product->save();
            /** Track Retail Inventory */
            $retailInventory = new RetailInventory();
            $retailInventory->product_id = $productId;
            $retailInventory->stock_qty = ($type == 'S' || $type == 'O')? -$stock_qty: $stock_qty;
            $retailInventory->transaction_type = $type;
            if($order_item_id){
                $retailInventory->order_item_id = $order_item_id;
            }
            $retailInventory->save();
            return $product;
        }
    }
    public static function validateStockAvailable($productId,$qty){
        $product = Product::where(['id' => $productId,'enable_retail_inventory'=>1])->first();
        if($product){
            if($product->stock_level < $qty){
                return false;
            }
        }
        return true;
    }
    public static function getInventoryEnabledProducts($request)
    {
        $pos = ProductType::where('name', '=', 'POS')->first();
        if (!$pos) {
            throw new \Exception('POS module not found');
        }
        $venue_id = $request->venue_id;
        $products = Product::select(
                'products.id as id',
                'products.name',
                'products.stock_level',
                'products.enable_retail_inventory',
            )->where(['products.status_id' => 1, 'products.venue_id' => $venue_id, 'products.product_type_id' => $pos->id]);
        return $products;
    }
}

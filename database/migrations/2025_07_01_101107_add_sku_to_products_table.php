<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSkuToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('sku', 32)->nullable()->after('name')->comment('Stock Keeping Unit - Unique product identifier');
            $table->unique(['sku', 'venue_id'], 'products_sku_venue_unique');
            $table->index('sku', 'products_sku_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('products_sku_index');
            $table->dropUnique('products_sku_venue_unique');
            $table->dropColumn('sku');
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSkuToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('sku', 32)->nullable()->after('name')->comment('Stock Keeping Unit - Unique product identifier');
            $table->unique(['sku', 'venue_id'], 'products_sku_venue_unique');
            $table->index('sku', 'products_sku_index');

            // Add venue_outlet_id field
            $table->unsignedBigInteger('venue_outlet_id')->nullable()->after('venue_id')->comment('Venue outlet assignment for product');
            $table->foreign('venue_outlet_id')->references('id')->on('venue_outlets');
            $table->index('venue_outlet_id', 'products_venue_outlet_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('products_sku_index');
            $table->dropUnique('products_sku_venue_unique');
            $table->dropColumn('sku');

            // Drop venue_outlet_id field
            $table->dropForeign(['venue_outlet_id']);
            $table->dropIndex('products_venue_outlet_index');
            $table->dropColumn('venue_outlet_id');
        });
    }
}

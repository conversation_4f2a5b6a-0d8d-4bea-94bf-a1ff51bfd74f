<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOutletForeignKeyToProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            // Add venue_outlet_id field for primary outlet assignment
            $table->unsignedBigInteger('venue_outlet_id')->nullable()->after('outlet_id')->comment('Primary venue outlet assignment for product');
            $table->foreign('venue_outlet_id')->references('id')->on('venue_outlets');
            $table->index('venue_outlet_id', 'products_venue_outlet_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['venue_outlet_id']);
            $table->dropIndex('products_venue_outlet_index');
            $table->dropColumn('venue_outlet_id');
        });
    }
}
